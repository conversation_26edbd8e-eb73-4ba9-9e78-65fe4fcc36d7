// Authentication and RBAC Context
// Manages JWT tokens, organization-scoped permissions, and role hierarchy

export type UserRole = "SUPER_ADMIN" | "ORG_ADMIN" | "DEVELOPER" | "VIEWER";

export interface Permission {
  module: string;
  action: string;
  resource?: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  organizationId: string;
  role: UserRole;
  permissions: Permission[];
  apiKeys: string[];
}

export interface Organization {
  id: string;
  name: string;
  slug: string;
  quotas: {
    agents: number;
    executions: number;
    storage: number;
    apiCalls: number;
  };
  usage: {
    agents: number;
    executions: number;
    storage: number;
    apiCalls: number;
  };
  branding: {
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
  };
}

export interface AuthContext {
  user: User | null;
  organization: Organization | null;
  token: string | null;
  isAuthenticated: boolean;
  hasPermission: (module: string, action: string, resource?: string) => boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  switchOrganization: (organizationId: string) => Promise<void>;
}

// Role hierarchy and permissions
const ROLE_HIERARCHY: Record<UserRole, number> = {
  SUPER_ADMIN: 4,
  ORG_ADMIN: 3,
  DEVELOPER: 2,
  VIEWER: 1,
};

const DEFAULT_PERMISSIONS: Record<UserRole, Permission[]> = {
  SUPER_ADMIN: [{ module: "*", action: "*" }],
  ORG_ADMIN: [
    { module: "agents", action: "*" },
    { module: "tools", action: "*" },
    { module: "hybrids", action: "*" },
    { module: "sessions", action: "*" },
    { module: "knowledge", action: "*" },
    { module: "widgets", action: "*" },
    { module: "analytics", action: "read" },
    { module: "billing", action: "read" },
    { module: "users", action: "*" },
  ],
  DEVELOPER: [
    { module: "agents", action: "*" },
    { module: "tools", action: "read" },
    { module: "hybrids", action: "*" },
    { module: "sessions", action: "read" },
    { module: "knowledge", action: "*" },
    { module: "widgets", action: "*" },
    { module: "analytics", action: "read" },
  ],
  VIEWER: [
    { module: "agents", action: "read" },
    { module: "tools", action: "read" },
    { module: "hybrids", action: "read" },
    { module: "sessions", action: "read" },
    { module: "knowledge", action: "read" },
    { module: "widgets", action: "read" },
    { module: "analytics", action: "read" },
  ],
};

export function hasPermission(
  userRole: UserRole,
  userPermissions: Permission[],
  module: string,
  action: string,
  resource?: string,
): boolean {
  // Super admin has all permissions
  if (userRole === "SUPER_ADMIN") {
    return true;
  }

  // Check explicit permissions
  const hasExplicitPermission = userPermissions.some((permission) => {
    const moduleMatch =
      permission.module === "*" || permission.module === module;
    const actionMatch =
      permission.action === "*" || permission.action === action;
    const resourceMatch =
      !permission.resource || !resource || permission.resource === resource;

    return moduleMatch && actionMatch && resourceMatch;
  });

  if (hasExplicitPermission) {
    return true;
  }

  // Check default role permissions
  const defaultPermissions = DEFAULT_PERMISSIONS[userRole] || [];
  return defaultPermissions.some((permission) => {
    const moduleMatch =
      permission.module === "*" || permission.module === module;
    const actionMatch =
      permission.action === "*" || permission.action === action;
    const resourceMatch =
      !permission.resource || !resource || permission.resource === resource;

    return moduleMatch && actionMatch && resourceMatch;
  });
}

export function canAccessRole(
  currentRole: UserRole,
  targetRole: UserRole,
): boolean {
  return ROLE_HIERARCHY[currentRole] >= ROLE_HIERARCHY[targetRole];
}

// Mock auth context for development
export const mockAuthContext: AuthContext = {
  user: {
    id: "user-123",
    email: "<EMAIL>",
    name: "Admin User",
    organizationId: "org-123",
    role: "ORG_ADMIN",
    permissions: DEFAULT_PERMISSIONS["ORG_ADMIN"],
    apiKeys: ["key-123", "key-456"],
  },
  organization: {
    id: "org-123",
    name: "SynapseAI Demo",
    slug: "synapseai-demo",
    quotas: {
      agents: 100,
      executions: 10000,
      storage: 1000000000, // 1GB
      apiCalls: 50000,
    },
    usage: {
      agents: 24,
      executions: 1284,
      storage: 245000000, // 245MB
      apiCalls: 12450,
    },
    branding: {
      logo: "/logo.png",
      primaryColor: "#3b82f6",
      secondaryColor: "#1e40af",
    },
  },
  token: "mock-jwt-token",
  isAuthenticated: true,
  hasPermission: (module: string, action: string, resource?: string) => {
    return hasPermission(
      "ORG_ADMIN",
      DEFAULT_PERMISSIONS["ORG_ADMIN"],
      module,
      action,
      resource,
    );
  },
  login: async () => {},
  logout: () => {},
  switchOrganization: async () => {},
};
