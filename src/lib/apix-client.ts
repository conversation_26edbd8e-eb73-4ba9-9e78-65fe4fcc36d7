// APIX Real-Time Engine Client
// Handles WebSocket connections, event streaming, and real-time state synchronization

interface APXEvent {
  id: string;
  type: string;
  module:
    | "agents"
    | "tools"
    | "hybrids"
    | "sessions"
    | "approvals"
    | "knowledge"
    | "widgets"
    | "analytics"
    | "billing"
    | "notifications";
  organizationId: string;
  userId: string;
  timestamp: string;
  data: any;
  version: number;
}

interface APXSubscription {
  id: string;
  module: string;
  eventTypes: string[];
  organizationId: string;
  callback: (event: APXEvent) => void;
}

class APXClient {
  private ws: WebSocket | null = null;
  private subscriptions: Map<string, APXSubscription> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private organizationId: string;
  private userId: string;
  private token: string;
  private eventBuffer: APXEvent[] = [];
  private isConnected = false;

  constructor(organizationId: string, userId: string, token: string) {
    this.organizationId = organizationId;
    this.userId = userId;
    this.token = token;
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const wsUrl = `${process.env.NEXT_PUBLIC_APIX_WS_URL || "wss://api.synapseai.com/apix"}?org=${this.organizationId}&token=${this.token}`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log("APIX WebSocket connected");
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.flushEventBuffer();
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const apxEvent: APXEvent = JSON.parse(event.data);
            this.handleEvent(apxEvent);
          } catch (error) {
            console.error("Error parsing APIX event:", error);
          }
        };

        this.ws.onclose = () => {
          console.log("APIX WebSocket disconnected");
          this.isConnected = false;
          this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
          console.error("APIX WebSocket error:", error);
          reject(error);
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(
          `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`,
        );
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  private handleEvent(event: APXEvent) {
    // Organization-scoped event filtering
    if (event.organizationId !== this.organizationId) {
      return; // Ignore events from other organizations
    }

    // Route event to subscribed handlers
    this.subscriptions.forEach((subscription) => {
      if (
        subscription.module === event.module &&
        subscription.eventTypes.includes(event.type)
      ) {
        subscription.callback(event);
      }
    });
  }

  private flushEventBuffer() {
    while (this.eventBuffer.length > 0) {
      const event = this.eventBuffer.shift();
      if (event) {
        this.sendEvent(event);
      }
    }
  }

  subscribe(
    module: string,
    eventTypes: string[],
    callback: (event: APXEvent) => void,
  ): string {
    const subscriptionId = `${module}_${Date.now()}_${Math.random()}`;
    const subscription: APXSubscription = {
      id: subscriptionId,
      module,
      eventTypes,
      organizationId: this.organizationId,
      callback,
    };

    this.subscriptions.set(subscriptionId, subscription);

    // Send subscription request to server
    if (this.isConnected) {
      this.sendEvent({
        id: `sub_${subscriptionId}`,
        type: "subscribe",
        module: "system" as any,
        organizationId: this.organizationId,
        userId: this.userId,
        timestamp: new Date().toISOString(),
        data: { module, eventTypes },
        version: 1,
      });
    }

    return subscriptionId;
  }

  unsubscribe(subscriptionId: string) {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      this.subscriptions.delete(subscriptionId);

      // Send unsubscribe request to server
      if (this.isConnected) {
        this.sendEvent({
          id: `unsub_${subscriptionId}`,
          type: "unsubscribe",
          module: "system" as any,
          organizationId: this.organizationId,
          userId: this.userId,
          timestamp: new Date().toISOString(),
          data: { subscriptionId },
          version: 1,
        });
      }
    }
  }

  sendEvent(event: APXEvent) {
    if (this.isConnected && this.ws) {
      this.ws.send(JSON.stringify(event));
    } else {
      // Buffer events when disconnected
      this.eventBuffer.push(event);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.subscriptions.clear();
    this.isConnected = false;
  }
}

// Singleton instance
let apixClient: APXClient | null = null;

export function getAPXClient(
  organizationId?: string,
  userId?: string,
  token?: string,
): APXClient {
  if (!apixClient && organizationId && userId && token) {
    apixClient = new APXClient(organizationId, userId, token);
  }

  if (!apixClient) {
    throw new Error(
      "APIX client not initialized. Please provide organizationId, userId, and token.",
    );
  }

  return apixClient;
}

export { APXClient, type APXEvent, type APXSubscription };
