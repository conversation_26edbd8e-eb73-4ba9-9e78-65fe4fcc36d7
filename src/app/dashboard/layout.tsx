"use client";

import React, { useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Bot,
  Wrench,
  Network,
  BarChart3,
  Database,
  Widgets,
  Users,
  Settings,
  BookOpen,
  TestTube,
  Menu,
  Bell,
  Search,
  User,
  LogOut,
  Shield,
  Building,
  Zap,
  Activity,
  Globe,
  ChevronDown,
  Home,
} from "lucide-react";
import { mockAuthContext } from "@/lib/auth-context";

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  description: string;
  module: string;
  requiredPermission?: string;
}

const navigationItems: NavigationItem[] = [
  {
    name: "Overview",
    href: "/dashboard",
    icon: Home,
    description: "Dashboard overview and quick stats",
    module: "dashboard",
  },
  {
    name: "Agents",
    href: "/dashboard/agents",
    icon: Bot,
    description: "AI agents with memory and reasoning",
    module: "agents",
    requiredPermission: "read",
  },
  {
    name: "Tools",
    href: "/dashboard/tools",
    icon: Wrench,
    description: "Stateless API tools and functions",
    module: "tools",
    requiredPermission: "read",
  },
  {
    name: "Hybrids",
    href: "/dashboard/hybrids",
    icon: Network,
    description: "Tool-Agent hybrid workflows",
    module: "hybrids",
    requiredPermission: "read",
  },
  {
    name: "Analytics",
    href: "/dashboard/analytics",
    icon: BarChart3,
    description: "Usage metrics and performance insights",
    module: "analytics",
    requiredPermission: "read",
  },
  {
    name: "Sessions",
    href: "/dashboard/sessions",
    icon: Activity,
    description: "Real-time session management",
    module: "sessions",
    requiredPermission: "read",
  },
  {
    name: "Knowledge Base",
    href: "/dashboard/knowledge",
    icon: BookOpen,
    description: "RAG documents and vector search",
    module: "knowledge",
    requiredPermission: "read",
  },
  {
    name: "Widgets",
    href: "/dashboard/widgets",
    icon: Widgets,
    description: "Embeddable AI components",
    module: "widgets",
    requiredPermission: "read",
  },
  {
    name: "Sandbox",
    href: "/dashboard/sandbox",
    icon: TestTube,
    description: "Test and debug environment",
    module: "sandbox",
    requiredPermission: "read",
  },
  {
    name: "Users & Roles",
    href: "/dashboard/users",
    icon: Users,
    description: "User management and RBAC",
    module: "users",
    requiredPermission: "read",
  },
  {
    name: "Settings",
    href: "/dashboard/settings",
    icon: Settings,
    description: "System configuration and preferences",
    module: "settings",
    requiredPermission: "read",
  },
];

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();
  const { user, organization, hasPermission } = mockAuthContext;

  // Filter navigation items based on user permissions
  const filteredNavItems = navigationItems.filter((item) => {
    if (!item.requiredPermission) return true;
    return hasPermission(item.module, item.requiredPermission);
  });

  const SidebarContent = () => (
    <div className="flex h-full flex-col">
      {/* Logo and Organization */}
      <div className="flex h-16 items-center border-b px-6">
        <div className="flex items-center space-x-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
            <Zap className="h-4 w-4 text-primary-foreground" />
          </div>
          <div className="flex flex-col">
            <span className="text-sm font-semibold">SynapseAI</span>
            <span className="text-xs text-muted-foreground">
              {organization?.name || "Default Org"}
            </span>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <nav className="space-y-1">
          {filteredNavItems.map((item) => {
            const isActive = item.href === "/dashboard"
              ? pathname === "/dashboard"
              : pathname.startsWith(item.href);
            const Icon = item.icon;

            return (
              <TooltipProvider key={item.name}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                        isActive
                          ? "bg-primary text-primary-foreground"
                          : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                      )}
                      onClick={() => setSidebarOpen(false)}
                    >
                      <Icon className="h-4 w-4" />
                      <span>{item.name}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="ml-auto">
                          {item.badge}
                        </Badge>
                      )}
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>{item.description}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            );
          })}
        </nav>
      </ScrollArea>

      {/* User Profile */}
      <div className="border-t p-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="w-full justify-start">
              <div className="flex items-center space-x-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary">
                  <User className="h-4 w-4 text-primary-foreground" />
                </div>
                <div className="flex flex-col items-start">
                  <span className="text-sm font-medium">{user?.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {user?.role}
                  </span>
                </div>
                <ChevronDown className="ml-auto h-4 w-4" />
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <User className="mr-2 h-4 w-4" />
              Profile
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Shield className="mr-2 h-4 w-4" />
              Security
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <LogOut className="mr-2 h-4 w-4" />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );

  return (
    <div className="flex h-screen bg-background">
      {/* Desktop Sidebar */}
      <div className="hidden w-64 border-r bg-card lg:block">
        <SidebarContent />
      </div>

      {/* Mobile Sidebar */}
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetContent side="left" className="w-64 p-0 sm:w-80">
          <SidebarContent />
        </SheetContent>
      </Sheet>

      {/* Main Content */}
      <div className="flex flex-1 flex-col overflow-hidden">
        {/* Top Header */}
        <header className="flex h-16 items-center border-b bg-card px-4 sm:px-6">
          <div className="flex items-center space-x-2 sm:space-x-4">
            <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="lg:hidden">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
            </Sheet>

            <div className="hidden sm:flex items-center space-x-2">
              <Badge variant="outline" className="flex items-center gap-1">
                <Building className="h-3 w-3" />
                <span className="hidden sm:inline">{organization?.name}</span>
              </Badge>
              <Badge variant="secondary" className="hidden sm:inline">
                {user?.role}
              </Badge>
            </div>
          </div>

          <div className="ml-auto flex items-center space-x-2 sm:space-x-4">
            <Button variant="ghost" size="icon" className="hidden sm:flex">
              <Search className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon">
              <Bell className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" className="hidden sm:flex">
              <Globe className="h-5 w-5" />
            </Button>

            {/* Mobile user menu */}
            <div className="sm:hidden">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <User className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium">{user?.name}</p>
                      <p className="text-xs text-muted-foreground">{user?.role}</p>
                      <p className="text-xs text-muted-foreground">{organization?.name}</p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <User className="mr-2 h-4 w-4" />
                    Profile
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <LogOut className="mr-2 h-4 w-4" />
                    Log out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  );
}
