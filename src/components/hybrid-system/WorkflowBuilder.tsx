"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "../ui/card";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Badge } from "../ui/badge";
import { ScrollArea } from "../ui/scroll-area";
import {
  Play,
  Save,
  Plus,
  Trash,
  Settings,
  GitBranch,
  Zap,
  Bot,
  Wrench,
  ArrowRight,
  ArrowDown,
  Diamond,
  Circle,
  Square,
  Wifi,
  WifiOff,
  Shield,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { getAPXClient } from "../../lib/apix-client";
import { mockAuthContext } from "../../lib/auth-context";
import { getSessionManager } from "../../lib/session-manager";
import { createHybridStepCompletedEvent } from "../../lib/apix-events";

interface WorkflowNode {
  id: string;
  type: "agent" | "tool" | "condition" | "start" | "end";
  name: string;
  position: { x: number; y: number };
  config: any;
  connections: string[];
}

interface WorkflowData {
  id: string;
  name: string;
  description: string;
  nodes: WorkflowNode[];
  version: string;
  isActive: boolean;
}

interface WorkflowBuilderProps {
  workflowId?: string;
  initialData?: WorkflowData;
  onSave?: (data: WorkflowData) => void;
  onExecute?: (data: WorkflowData) => void;
}

const WorkflowBuilder: React.FC<WorkflowBuilderProps> = ({
  workflowId,
  initialData,
  onSave = () => {},
  onExecute = () => {},
}) => {
  const defaultData: WorkflowData = {
    id: workflowId || `workflow-${Date.now()}`,
    name: "",
    description: "",
    nodes: [
      {
        id: "start",
        type: "start",
        name: "Start",
        position: { x: 100, y: 100 },
        config: {},
        connections: [],
      },
      {
        id: "end",
        type: "end",
        name: "End",
        position: { x: 500, y: 300 },
        config: {},
        connections: [],
      },
    ],
    version: "1.0.0",
    isActive: false,
  };

  const [workflowData, setWorkflowData] = useState<WorkflowData>(
    initialData || defaultData,
  );
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionLog, setExecutionLog] = useState<string[]>([]);
  const [draggedNodeType, setDraggedNodeType] = useState<string | null>(null);

  // APIX Real-Time Engine state
  const [isConnected, setIsConnected] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Auth & RBAC state
  const {
    user,
    organization,
    hasPermission: checkPermission,
  } = mockAuthContext;
  const canCreateWorkflow = checkPermission("hybrids", "create");
  const canEditWorkflow = checkPermission("hybrids", "update");
  const canExecuteWorkflow = checkPermission("hybrids", "execute");

  // Session Manager
  const sessionManager = getSessionManager(organization?.id || "default");

  // Initialize APIX connection and session
  useEffect(() => {
    const initializeAPX = async () => {
      try {
        if (user && organization) {
          const apixClient = getAPXClient(
            organization.id,
            user.id,
            mockAuthContext.token!,
          );
          await apixClient.connect();
          setIsConnected(true);

          // Subscribe to hybrid events
          apixClient.subscribe(
            "hybrids",
            ["started", "step_completed", "finished", "execution_failed"],
            handleRealtimeEvent,
          );

          // Create session for workflow builder
          const newSessionId = await sessionManager.createSession(
            "hybrids",
            "user",
            { workflowId, builderState: workflowData },
            {
              tags: ["workflow-builder"],
              conversationId: `builder-${Date.now()}`,
            },
          );
          setSessionId(newSessionId);
        }
      } catch (error) {
        console.error("Failed to initialize APIX:", error);
        setIsConnected(false);
      }
    };

    initializeAPX();

    return () => {
      if (sessionId) {
        sessionManager.deleteSession(sessionId);
      }
    };
  }, [user, organization]);

  const handleRealtimeEvent = useCallback(
    (event: any) => {
      switch (event.type) {
        case "step_completed":
          if (event.data.hybridId === workflowData.id) {
            setExecutionLog((prev) => [
              ...prev,
              `Step ${event.data.stepNumber}: ${event.data.stepType} completed in ${event.data.duration}ms`,
            ]);
          }
          break;
        case "finished":
          if (event.data.hybridId === workflowData.id) {
            setIsExecuting(false);
            setExecutionLog((prev) => [
              ...prev,
              `Workflow completed successfully in ${event.data.totalDuration}ms`,
            ]);
          }
          break;
        case "execution_failed":
          if (event.data.hybridId === workflowData.id) {
            setIsExecuting(false);
            setExecutionLog((prev) => [
              ...prev,
              `Workflow failed at step ${event.data.failedStep}: ${event.data.error}`,
            ]);
          }
          break;
      }
    },
    [workflowData.id],
  );

  const addNode = (
    type: "agent" | "tool" | "condition",
    position: { x: number; y: number },
  ) => {
    const newNode: WorkflowNode = {
      id: `${type}-${Date.now()}`,
      type,
      name: `${type.charAt(0).toUpperCase() + type.slice(1)} ${workflowData.nodes.length}`,
      position,
      config: {},
      connections: [],
    };

    setWorkflowData((prev) => ({
      ...prev,
      nodes: [...prev.nodes, newNode],
    }));
  };

  const updateNode = (nodeId: string, updates: Partial<WorkflowNode>) => {
    setWorkflowData((prev) => ({
      ...prev,
      nodes: prev.nodes.map((node) =>
        node.id === nodeId ? { ...node, ...updates } : node,
      ),
    }));
  };

  const deleteNode = (nodeId: string) => {
    if (nodeId === "start" || nodeId === "end") return;

    setWorkflowData((prev) => ({
      ...prev,
      nodes: prev.nodes.filter((node) => node.id !== nodeId),
    }));

    if (selectedNode?.id === nodeId) {
      setSelectedNode(null);
    }
  };

  const connectNodes = (fromId: string, toId: string) => {
    setWorkflowData((prev) => ({
      ...prev,
      nodes: prev.nodes.map((node) =>
        node.id === fromId
          ? { ...node, connections: [...node.connections, toId] }
          : node,
      ),
    }));
  };

  const handleSave = async () => {
    if (!canCreateWorkflow && !canEditWorkflow) {
      alert("You do not have permission to save workflows.");
      return;
    }

    try {
      if (!workflowData.name.trim()) {
        throw new Error("Workflow name is required");
      }

      // Update session with current state
      if (sessionId) {
        await sessionManager.updateSession(sessionId, {
          builderState: workflowData,
          lastSaved: new Date().toISOString(),
        });
      }

      // Broadcast save event via APIX
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `workflow-saved-${Date.now()}`,
          type: workflowId ? "updated" : "created",
          module: "hybrids",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            workflowId: workflowData.id,
            workflowData,
          },
          version: 1,
        });
      }

      onSave(workflowData);
    } catch (error) {
      console.error("Error saving workflow:", error);
      alert(
        `Error saving workflow: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  };

  const handleExecute = async () => {
    if (!canExecuteWorkflow) {
      alert("You do not have permission to execute workflows.");
      return;
    }

    setIsExecuting(true);
    setExecutionLog(["Starting workflow execution..."]);

    try {
      // Create execution session
      const executionSessionId = await sessionManager.createSession(
        "hybrids",
        "hybrid",
        {
          workflowConfig: workflowData,
          startTime: new Date().toISOString(),
        },
        {
          parentSessionId: sessionId,
          tags: ["execution", "hybrid-workflow"],
          conversationId: `execution-${Date.now()}`,
        },
      );

      // Broadcast execution start event
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `hybrid-started-${Date.now()}`,
          type: "hybrid_started",
          module: "hybrids",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            hybridId: workflowData.id,
            workflowId: workflowData.id,
            agentId: "workflow-agent",
            tools: workflowData.nodes
              .filter((n) => n.type === "tool")
              .map((n) => n.id),
            input: {},
            expectedSteps: workflowData.nodes.length,
          },
          version: 1,
        });
      }

      // Simulate workflow execution
      const executionSteps = workflowData.nodes.filter(
        (n) => n.type !== "start" && n.type !== "end",
      );

      for (let i = 0; i < executionSteps.length; i++) {
        const step = executionSteps[i];
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Broadcast step completion
        if (isConnected && user && organization) {
          const apixClient = getAPXClient(
            organization.id,
            user.id,
            mockAuthContext.token!,
          );

          const stepEvent = createHybridStepCompletedEvent(
            organization.id,
            user.id,
            {
              hybridId: workflowData.id,
              stepId: step.id,
              stepType:
                step.type === "agent"
                  ? "agent_reasoning"
                  : step.type === "tool"
                    ? "tool_execution"
                    : "conditional_logic",
              stepNumber: i + 1,
              output: { success: true, data: `Step ${i + 1} completed` },
              duration: 1000,
              nextStep:
                i < executionSteps.length - 1
                  ? executionSteps[i + 1].id
                  : undefined,
            },
          );

          apixClient.sendEvent(stepEvent);
        }
      }

      // Broadcast completion
      if (isConnected && user && organization) {
        const apixClient = getAPXClient(
          organization.id,
          user.id,
          mockAuthContext.token!,
        );
        apixClient.sendEvent({
          id: `hybrid-finished-${Date.now()}`,
          type: "hybrid_finished",
          module: "hybrids",
          organizationId: organization.id,
          userId: user.id,
          timestamp: new Date().toISOString(),
          data: {
            hybridId: workflowData.id,
            totalSteps: executionSteps.length,
            totalDuration: executionSteps.length * 1000,
            finalOutput: {
              success: true,
              message: "Workflow completed successfully",
            },
            success: true,
            cost: 0.25,
          },
          version: 1,
        });
      }

      onExecute(workflowData);
    } catch (error) {
      console.error("Error executing workflow:", error);
      setExecutionLog((prev) => [
        ...prev,
        `Execution failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      ]);
      setIsExecuting(false);
    }
  };

  const getNodeIcon = (type: string) => {
    switch (type) {
      case "agent":
        return <Bot className="h-4 w-4" />;
      case "tool":
        return <Wrench className="h-4 w-4" />;
      case "condition":
        return <Diamond className="h-4 w-4" />;
      case "start":
        return <Circle className="h-4 w-4" />;
      case "end":
        return <Square className="h-4 w-4" />;
      default:
        return <Circle className="h-4 w-4" />;
    }
  };

  const getNodeColor = (type: string) => {
    switch (type) {
      case "agent":
        return "bg-blue-100 border-blue-300 text-blue-700";
      case "tool":
        return "bg-green-100 border-green-300 text-green-700";
      case "condition":
        return "bg-yellow-100 border-yellow-300 text-yellow-700";
      case "start":
        return "bg-gray-100 border-gray-300 text-gray-700";
      case "end":
        return "bg-red-100 border-red-300 text-red-700";
      default:
        return "bg-gray-100 border-gray-300 text-gray-700";
    }
  };

  return (
    <div className="bg-background w-full h-full flex flex-col">
      <div className="flex justify-between items-center p-4 border-b">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold">
              {workflowId ? "Edit Workflow" : "Create New Workflow"}
            </h1>
            <div className="flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    {isConnected ? (
                      <Wifi className="h-4 w-4 text-green-500" />
                    ) : (
                      <WifiOff className="h-4 w-4 text-red-500" />
                    )}
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>
                      {isConnected
                        ? "Connected to APIX"
                        : "Disconnected from APIX"}
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Badge variant="outline" className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                {user?.role}
              </Badge>
              <Badge variant="secondary">{organization?.name}</Badge>
            </div>
          </div>
          <div className="flex items-center gap-4 mt-1">
            <Input
              value={workflowData.name}
              onChange={(e) =>
                setWorkflowData((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="Workflow name..."
              className="w-64"
            />
            <Input
              value={workflowData.description}
              onChange={(e) =>
                setWorkflowData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Description..."
              className="w-96"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleSave}
            disabled={!canCreateWorkflow && !canEditWorkflow}
          >
            <Save className="mr-2 h-4 w-4" /> Save Workflow
          </Button>
          <Button
            onClick={handleExecute}
            disabled={isExecuting || !canExecuteWorkflow}
            variant="default"
          >
            {isExecuting ? (
              <>
                <span className="animate-spin mr-2">⏳</span> Executing...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" /> Execute
              </>
            )}
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Node Palette */}
        <div className="w-64 border-r p-4 space-y-4">
          <h3 className="font-semibold">Node Types</h3>
          <div className="space-y-2">
            {[
              {
                type: "agent",
                label: "AI Agent",
                icon: <Bot className="h-4 w-4" />,
              },
              {
                type: "tool",
                label: "Tool",
                icon: <Wrench className="h-4 w-4" />,
              },
              {
                type: "condition",
                label: "Condition",
                icon: <Diamond className="h-4 w-4" />,
              },
            ].map(({ type, label, icon }) => (
              <Button
                key={type}
                variant="outline"
                className="w-full justify-start"
                onClick={() => addNode(type as any, { x: 200, y: 200 })}
              >
                {icon}
                <span className="ml-2">{label}</span>
              </Button>
            ))}
          </div>

          {/* Execution Log */}
          {executionLog.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-semibold">Execution Log</h3>
              <ScrollArea className="h-32 border rounded p-2">
                <div className="space-y-1">
                  {executionLog.map((log, index) => (
                    <div key={index} className="text-xs text-muted-foreground">
                      {log}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}
        </div>

        {/* Canvas */}
        <div className="flex-1 relative overflow-auto bg-gray-50">
          <div className="absolute inset-0 p-4">
            <svg className="absolute inset-0 w-full h-full pointer-events-none">
              {/* Grid pattern */}
              <defs>
                <pattern
                  id="grid"
                  width="20"
                  height="20"
                  patternUnits="userSpaceOnUse"
                >
                  <path
                    d="M 20 0 L 0 0 0 20"
                    fill="none"
                    stroke="#e5e7eb"
                    strokeWidth="1"
                  />
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />

              {/* Connection lines */}
              {workflowData.nodes
                .map((node) =>
                  node.connections.map((targetId) => {
                    const target = workflowData.nodes.find(
                      (n) => n.id === targetId,
                    );
                    if (!target) return null;
                    return (
                      <line
                        key={`${node.id}-${targetId}`}
                        x1={node.position.x + 60}
                        y1={node.position.y + 30}
                        x2={target.position.x + 60}
                        y2={target.position.y + 30}
                        stroke="#6b7280"
                        strokeWidth="2"
                        markerEnd="url(#arrowhead)"
                      />
                    );
                  }),
                )
                .flat()}

              {/* Arrow marker */}
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
                </marker>
              </defs>
            </svg>

            {/* Nodes */}
            {workflowData.nodes.map((node) => (
              <div
                key={node.id}
                className={`absolute w-32 h-16 border-2 rounded-lg p-2 cursor-pointer transition-all hover:shadow-md ${
                  selectedNode?.id === node.id ? "ring-2 ring-blue-500" : ""
                } ${getNodeColor(node.type)}`}
                style={{
                  left: node.position.x,
                  top: node.position.y,
                }}
                onClick={() => setSelectedNode(node)}
              >
                <div className="flex items-center gap-2">
                  {getNodeIcon(node.type)}
                  <span className="text-xs font-medium truncate">
                    {node.name}
                  </span>
                </div>
                <div className="text-xs opacity-75 capitalize">{node.type}</div>
                {node.id !== "start" && node.id !== "end" && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute -top-2 -right-2 h-6 w-6 p-0 bg-red-500 text-white hover:bg-red-600"
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteNode(node.id);
                    }}
                  >
                    <Trash className="h-3 w-3" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Properties Panel */}
        {selectedNode && (
          <div className="w-80 border-l p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">Node Properties</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedNode(null)}
              >
                ×
              </Button>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getNodeIcon(selectedNode.type)}
                  {selectedNode.type.charAt(0).toUpperCase() +
                    selectedNode.type.slice(1)}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Name</Label>
                  <Input
                    value={selectedNode.name}
                    onChange={(e) =>
                      updateNode(selectedNode.id, { name: e.target.value })
                    }
                  />
                </div>

                {selectedNode.type === "agent" && (
                  <div className="space-y-2">
                    <Label>Agent Configuration</Label>
                    <div className="text-sm text-muted-foreground">
                      Configure the AI agent settings, model, and prompt
                      template.
                    </div>
                    <Button variant="outline" size="sm">
                      <Settings className="mr-2 h-4 w-4" /> Configure Agent
                    </Button>
                  </div>
                )}

                {selectedNode.type === "tool" && (
                  <div className="space-y-2">
                    <Label>Tool Configuration</Label>
                    <div className="text-sm text-muted-foreground">
                      Select and configure the tool to execute.
                    </div>
                    <Button variant="outline" size="sm">
                      <Wrench className="mr-2 h-4 w-4" /> Select Tool
                    </Button>
                  </div>
                )}

                {selectedNode.type === "condition" && (
                  <div className="space-y-2">
                    <Label>Condition Logic</Label>
                    <div className="text-sm text-muted-foreground">
                      Define the conditional logic for branching.
                    </div>
                    <Button variant="outline" size="sm">
                      <GitBranch className="mr-2 h-4 w-4" /> Configure Logic
                    </Button>
                  </div>
                )}

                <div className="space-y-2">
                  <Label>Position</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      type="number"
                      value={selectedNode.position.x}
                      onChange={(e) =>
                        updateNode(selectedNode.id, {
                          position: {
                            ...selectedNode.position,
                            x: parseInt(e.target.value),
                          },
                        })
                      }
                      placeholder="X"
                    />
                    <Input
                      type="number"
                      value={selectedNode.position.y}
                      onChange={(e) =>
                        updateNode(selectedNode.id, {
                          position: {
                            ...selectedNode.position,
                            y: parseInt(e.target.value),
                          },
                        })
                      }
                      placeholder="Y"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Connections</Label>
                  <div className="text-sm text-muted-foreground">
                    Connected to: {selectedNode.connections.length} nodes
                  </div>
                  {selectedNode.connections.map((connId) => {
                    const connectedNode = workflowData.nodes.find(
                      (n) => n.id === connId,
                    );
                    return connectedNode ? (
                      <Badge key={connId} variant="outline">
                        {connectedNode.name}
                      </Badge>
                    ) : null;
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkflowBuilder;
